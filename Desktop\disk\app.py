
import pytesseract
from PIL import Image
import os

# IMPORTANT: You need to install Tesseract OCR engine on your system.
# For Windows: Download from https://tesseract-ocr.github.io/tessdoc/Downloads.html
# For macOS: brew install tesseract
# For Linux (Debian/Ubuntu): sudo apt-get install tesseract-ocr

# If Tesseract is not in your PATH, uncomment and set the path to its executable
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe' # Example for Windows
# pytesseract.pytesseract.tesseract_cmd = r'/usr/local/bin/tesseract' # Example for macOS

def extract_text_from_image(image_path, lang='eng'):
    """Extracts text from an image file using Tesseract OCR."""
    try:
        img = Image.open(image_path)
        text = pytesseract.image_to_string(img, lang=lang)
        return text
    except Exception as e:
        print(f"Error during OCR: {e}")
        return None

def check_target_text(text, target_phrase="Spirit Attack capacity Perfect +60%"):
    """Checks if the target phrase is present on a single line in the extracted text."""
    if text is None:
        return False
    lines = text.split('\n')
    for line in lines:
        if target_phrase in line and "Perfect" in line and "+60%" in line:
            return True
    return False

# --- User Integration Guide ---
# This script provides the core OCR functionality. To build the full automation,
# you will need to integrate it with screen capture and UI interaction libraries
# like `pyautogui` or `pynput` on your local machine.

# Here's a pseudocode example of how you might use this script:
"""
import pyautogui
import time

# Assuming extract_text_from_image and check_target_text are in this file or imported

# Define the region of the game UI where the item description appears.
# You will need to adjust these coordinates based on your screen resolution and game UI.
# To find coordinates: Use a tool like Greenshot (Windows) or built-in screenshot tools.
# Or, you can use pyautogui.displayMousePosition() in a separate script to get live coordinates.
ITEM_DESCRIPTION_REGION = (X, Y, WIDTH, HEIGHT) # Example: (100, 200, 300, 400)

# Define the coordinates of the 'OK' button.
OK_BUTTON_COORDS = (OK_X, OK_Y) # Example: (500, 600)

while True:
    # 1. Capture a screenshot of the relevant game UI area
    screenshot = pyautogui.screenshot(region=ITEM_DESCRIPTION_REGION)
    screenshot_path = "current_item_screenshot.png"
    screenshot.save(screenshot_path)

    # 2. Extract text using the OCR function
    item_text = extract_text_from_image(screenshot_path)
    print(f"Extracted Text:\n{item_text}")

    # 3. Check if the target stat is found
    if check_target_text(item_text):
        print("Success: Target stat 'Spirit Attack capacity Perfect +60%' found!")
        # Optional: Add visual feedback (e.g., draw a highlight on the screenshot)
        # You would need an image processing library like OpenCV for this.
        # Example (requires OpenCV):
        # img = cv2.imread(screenshot_path)
        # # Find coordinates of the text within the screenshot and draw a rectangle
        # # This part is complex and depends on OCR output details (bounding boxes).
        # cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 2) # Red rectangle
        # cv2.imwrite("highlighted_screenshot.png", img)
        break # Stop the loop
    else:
        print("Target stat NOT found. Clicking OK and retrying...")
        # 4. Click the 'OK' button
        pyautogui.click(OK_BUTTON_COORDS[0], OK_BUTTON_COORDS[1])

        # 5. Wait for the game UI to update. Adjust this delay as needed.
        time.sleep(2) # Wait for 2 seconds for the new item to appear

    # Clean up the temporary screenshot file
    if os.path.exists(screenshot_path):
        os.remove(screenshot_path)
"""

# Example of how to use the OCR functions with a dummy image for testing:
if __name__ == "__main__":
    # Create a dummy image for testing purposes
    dummy_image_path = "dummy_game_ui.png"
    try:
        # This part requires Pillow to create an image
        img = Image.new('RGB', (400, 200), color = 'white')
        from PIL import ImageDraw, ImageFont
        d = ImageDraw.Draw(img)
        # You might need to specify a font path if default is not found
        try:
            fnt = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', 20) # Example font path for Linux
        except IOError:
            fnt = ImageFont.load_default() # Fallback to default font

        d.text((10,10), "Some random text", fill=(0,0,0), font=fnt)
        d.text((10,50), "Spirit Attack capacity Perfect +60%", fill=(0,0,0), font=fnt)
        d.text((10,90), "Another line of text", fill=(0,0,0), font=fnt)
        img.save(dummy_image_path)
        print(f"Created dummy image: {dummy_image_path}")

        extracted_text = extract_text_from_image(dummy_image_path)
        print("\nExtracted Text from dummy image:")
        print(extracted_text)

        if check_target_text(extracted_text):
            print("\nTarget text 'Spirit Attack capacity Perfect +60%' found in dummy image!")
        else:
            print("\nTarget text 'Spirit Attack capacity Perfect +60%' NOT found in dummy image.")

    except ImportError:
        print("Pillow not fully installed or font issues. Cannot create dummy image for testing.")
        print("Please ensure Pillow is installed: pip install Pillow")
    finally:
        if os.path.exists(dummy_image_path):
            os.remove(dummy_image_path)




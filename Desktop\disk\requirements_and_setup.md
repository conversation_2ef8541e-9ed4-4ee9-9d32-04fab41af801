# Equipment Reappraisal Bot - Installation and Setup Guide

## 📋 Requirements

### Python Dependencies (requirements.txt)
```
pyautogui==0.9.54
pytesseract==0.3.10
opencv-python==********
Pillow==10.0.1
numpy==1.24.3
keyboard==0.13.5
```

### System Requirements
- **Python 3.7+** (Recommended: Python 3.9 or higher)
- **Tesseract OCR** (Required for text recognition)
- **Windows/Linux/Mac** (Tested on Windows 10/11)

## 🚀 Installation Steps

### 1. Download and Extract
1. Download the `equipment_reappraisal_bot.py` file
2. Create a new folder for the bot (e.g., `GameBot`)
3. Place the Python file in this folder

### 2. Install Python Dependencies
Open Command Prompt/Terminal in the bot folder and run:
```bash
pip install pyautogui pytesseract opencv-python Pillow numpy keyboard
```

Or create a `requirements.txt` file with the above dependencies and run:
```bash
pip install -r requirements.txt
```

### 3. Install Tesseract OCR

#### Windows:
1. Download Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki
2. Install the `.exe` file (recommended path: `C:\Program Files\Tesseract-OCR\`)
3. The bot will automatically detect the installation

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install tesseract-ocr
```

#### macOS:
```bash
brew install tesseract
```

### 4. Run the Application
```bash
python equipment_reappraisal_bot.py
```

## 🎯 How to Use

### Initial Setup
1. **Launch the bot** - Run the Python script
2. **Configure target text** - The default is "Spirit Attack capacity Perfect +60%"
3. **Set wait time** - Adjust the delay between actions (recommended: 2-3 seconds)
4. **Select OK button** - Click "Select OK Button" and follow the instructions

### Running the Bot
1. **Position your game window** - Make sure the Equipment Reappraisal dialog is visible
2. **Click "Start Bot"** - The bot will begin scanning and clicking
3. **Monitor the log** - Watch for status updates and found text
4. **Stop if needed** - Use the "Stop Bot" button to halt execution

### Configuration Options
- **Target Text**: The exact text to search for
- **Wait Time**: Delay between actions (seconds)
- **Max Loops**: Maximum number of attempts before stopping
- **Debug Mode**: Saves screenshots for troubleshooting

## 🛠️ Troubleshooting

### Common Issues

#### "Tesseract not found" Error
- Ensure Tesseract is properly installed
- Check if the path is correct in your system PATH
- Try reinstalling Tesseract

#### OCR Not Working Properly
- Ensure your game window is clearly visible
- Try increasing the wait time
- Enable debug mode to see what text is being detected
- Check if the game text is clear and readable

#### Bot Not Clicking Correctly
- Verify the OK button position is correctly set
- Make sure your game window hasn't moved
- Check if the game is in focus

#### Permission Denied Errors
- Run as administrator (Windows)
- Use `sudo` if needed (Linux/Mac)
- Check antivirus software isn't blocking the bot

### Debug Features
- **Debug Mode**: Saves screenshots to `debug_screenshots/` folder
- **Test OCR**: Tests text recognition on current screen
- **Log Window**: Shows real-time status and errors

## 📁 File Structure
```
GameBot/
├── equipment_reappraisal_bot.py    # Main application
├── bot_config.json                 # Saved configuration
├── requirements.txt                # Python dependencies
├── debug_screenshots/              # Debug images (if enabled)
└── highlights/                     # Success screenshots
```

## ⚠️ Important Notes

### Safety Considerations
- **Use responsibly** - Only use on games that allow automation
- **Monitor the bot** - Don't leave it running unattended for long periods
- **Backup your game** - Save your progress before using automation tools

### Performance Tips
- **Close other applications** - Free up CPU and RAM
- **Use a stable internet connection** - For online games
- **Monitor system resources** - The bot uses OCR which can be CPU-intensive

### Customization
- **Modify target text** - Change the search criteria as needed
- **Adjust timing** - Fine-tune wait times for your system
- **Add new features** - The code is well-commented for easy modification

## 🔄 Updates and Versions

### Version 1.0 Features
- ✅ OCR text recognition
- ✅ Automated clicking
- ✅ GUI interface
- ✅ Configuration saving
- ✅ Debug mode
- ✅ Loop counting
- ✅ Error handling

### Planned Features
- 🔄 Better area selection tool
- 🔄 Multiple target text support
- 🔄 Statistics tracking
- 🔄 Sound notifications
- 🔄 Hotkey support

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Enable debug mode to see detailed logs
3. Verify all dependencies are installed correctly
4. Make sure your game window is visible and accessible

## 📄 License

This tool is provided for educational purposes. Use responsibly and in accordance with your game's terms of service.

# Game UI Automation Script

This Python script automates the process of reappraising equipment in a game until a specific stat is found. It uses OCR to read the item's stats and simulates mouse clicks to interact with the game's UI.

## Features

-   **Automated UI Interaction:** Automatically clicks the "OK" button to reappraise an item.
-   **OCR-based Text Recognition:** Scans the item description for the target stat: "Spirit Attack capacity Perfect +60%".
-   **Looping until Condition Met:** Continuously reappraises the item until the desired stat is found.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

-   **Python 3:** [Download Python](https://www.python.org/downloads/)
-   **Tesseract OCR Engine:**
    -   **Windows:** Download and install from [Tesseract at UB Mannheim](https://github.com/UB-Mannheim/tesseract/wiki). Make sure to add the installation directory to your system's PATH.
    -   **macOS:** Install using Homebrew: `brew install tesseract`
    -   **Linux (Debian/Ubuntu):** Install using apt: `sudo apt-get install tesseract-ocr`

## Installation

1.  **<PERSON>lone or download this repository.**

2.  **Install the required Python libraries:**

    ```bash
    pip install pyautogui pytesseract opencv-python Pillow
    ```

## How to Use

1.  **Configure the script:**

    Open the `app.py` file and modify the following variables to match your screen setup:

    -   `ITEM_DESCRIPTION_REGION`: The coordinates of the item description area on your screen. This is a tuple of `(left, top, width, height)`.
    -   `OK_BUTTON_COORDS`: The `(x, y)` coordinates of the "OK" button in the game's UI.

    To find these coordinates, you can use the `pyautogui.displayMousePosition()` function in a separate Python script. Run this script and move your mouse to the desired locations to get the coordinates.

2.  **Run the script:**

    Open a terminal or command prompt, navigate to the script's directory, and run:

    ```bash
    python app.py
    ```

3.  **Supervise the process:**

    The script will now start capturing screenshots, analyzing the text, and clicking the "OK" button. You can monitor the progress in the terminal.

    **Important:** To stop the script, press `Ctrl+C` in the terminal.

## Disclaimer

This script is intended for educational purposes and to automate repetitive tasks. Please use it responsibly and be aware of the terms of service of the game you are playing. The developers of this script are not responsible for any misuse or consequences of using this software.



# 🔧 COMPLETE SETUP GUIDE - Equipment Reappraisal Bot

## 🚨 CRITICAL FIXES APPLIED

### ✅ **Issue 1: Scan Area Problem - FIXED!**
**Problem**: Your scan area was `(1249, 570, 0, 0)` - width and height were 0!
**Solution**: New scan area selection uses SPACE key for precise positioning

### ✅ **Issue 2: OK Button Not Clicking - FIXED!**
**Problem**: <PERSON><PERSON> said "clicked" but game didn't respond
**Solution**: Multiple clicking methods + better error handling

---

## 📋 STEP-BY-STEP SETUP

### **Step 1: Start the Bot**
```bash
py gui_app.py
```

### **Step 2: Set Item Hover Position**
1. Click **"Select Item Hover Position"**
2. Position mouse over the **ITEM** (not tooltip) in your game
3. Wait 5 seconds - bot will capture position
4. ✅ Should show: `Item hover position set to: Point(x=1029, y=490)`
 
### **Step 3: Set Scan Area (CRITICAL!)**
1. Click **"Select Scan Area"**
2. **Hover over item** to show tooltip
3. Press **SPACE** at **top-left corner** of tooltip
4. Move mouse to **bottom-right corner** of tooltip
5. Press **SPACE** again
6. ✅ Should show: `Scan area set to: (x, y, width, height)` with width/height > 0

### **Step 4: Set OK Button Position**
1. Click **"Select OK Button Position"**
2. Position mouse over the **OK button** in game
3. Wait 5 seconds - bot will capture position
4. ✅ Should show: `OK button position set to: Point(x=1022, y=596)`

### **Step 5: Test Everything**
1. Click **"Test OCR"** - should read tooltip text
2. Use **"Save Debug Screenshot"** to verify scan area
3. Run **`py ok_button_test.py`** to test clicking

### **Step 6: Start Bot**
1. Press **F1** or click **"Start Bot"**
2. Bot should hover → scan → click OK → repeat

---

## 🔍 TROUBLESHOOTING

### **Problem: "Scan area set to: (x, y, 0, 0)"**
**Cause**: Both corner positions are the same
**Solution**: 
- Make sure tooltip is visible when selecting corners
- Press SPACE at different positions (top-left vs bottom-right)
- Use `py hover_test.py` to practice

### **Problem: "OK button clicked!" but nothing happens**
**Cause**: Game window not focused or wrong position
**Solution**:
- Make sure game window is active
- Re-select OK button position
- Use `py ok_button_test.py` to test clicking
- Try different click methods in the test

### **Problem: "Target not found" but you can see the stats**
**Cause**: Scan area doesn't cover the tooltip
**Solution**:
- Re-select scan area to cover entire tooltip
- Make sure tooltip is visible during selection
- Use "Test OCR" to verify text recognition

### **Problem: Bot takes full screen screenshots**
**Cause**: Invalid scan area (width/height = 0)
**Solution**:
- Reset scan area: Click "Reset Scan Area"
- Re-select with proper SPACE key timing
- Verify area shows positive width/height

---

## 🧪 TESTING TOOLS

### **1. Hover Test (`py hover_test.py`)**
- Practice hover + scan area selection
- See exactly what OCR reads
- Perfect your positioning

### **2. OK Button Test (`py ok_button_test.py`)**
- Test if OK button actually responds
- Try different clicking methods
- Verify game window focus

### **3. Click Test (`py click_test.py`)**
- General clicking verification
- Compare hover vs click behavior

---

## ✅ VERIFICATION CHECKLIST

Before starting the bot, verify:

- [ ] **Hover Position**: Shows valid coordinates
- [ ] **Scan Area**: Width and height > 0 (not 0, 0)
- [ ] **OK Button**: Shows valid coordinates  
- [ ] **Target Text**: Entered in text field
- [ ] **Test OCR**: Can read tooltip text
- [ ] **Game Window**: Visible and focused

### **Expected Log Output:**
```
[20:30:01] Loop 1: Hovering over item...
[20:30:02] Taking screenshot while STILL HOVERING over item...
[20:30:03] Screenshot taken while hovering - scan area: (100, 100, 200, 150)
[20:30:04] OCR while hovering - text preview: Spirit Attack capacity...
[20:30:04] Target not found, now clicking OK button...
[20:30:04] Clicking OK button at position: Point(x=1022, y=596)
[20:30:04] ✅ OK button clicked with multiple methods!
```

---

## 🎯 QUICK FIX COMMANDS

If you have issues:

```bash
# Reset everything and start fresh
py gui_app.py
# Click "Reset All Settings"

# Test scan area selection
py hover_test.py

# Test OK button clicking  
py ok_button_test.py

# Test general clicking
py click_test.py
```

---

## 🚀 READY TO USE!

Once setup is complete:
1. **F1** = Start bot
2. **F2** = Stop bot  
3. Monitor the log for progress
4. Bot will automatically find your target item!

**The critical issues are now fixed - the bot should work properly!**
